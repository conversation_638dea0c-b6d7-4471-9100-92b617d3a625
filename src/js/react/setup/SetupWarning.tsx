import React from 'react';
import { Icon } from '../common/components/Icon';
import { useAppState } from '../state';
import { getDeniedParams, getParamTitle } from '../common/functions/helpers';

export const SetupWarning = () => {
	const { generalTranslations, paramTranslations, showDenied, setShowDenied, parameters, deniedBy } = useAppState((state) => ({
		generalTranslations: state.generalTranslations,
		paramTranslations: state.paramTranslations,
		showDenied: state.showDenied,
		setShowDenied: state.setShowDenied,
		deniedBy: state.deniedBy,
		parameters: state.parameters,
	}));

	const deniedParams = parameters ? Object.values(getDeniedParams(parameters, deniedBy)).flat() : null;

	return showDenied ? (
		<p className="b-setup__warning message message--warning message--light u-mb-0">
			<span className="item-icon u-fw-m">
				<Icon name="warning-bd" className="item-icon__icon" />
				<span className="item-icon__text">
					{generalTranslations['combination_error']}{' '}
					{deniedParams?.map((deniedParam) => getParamTitle(deniedParam, paramTranslations)).join(', ')}
				</span>
			</span>
			<button type="button" className="message__close as-link" onClick={() => setShowDenied(false)}>
				<Icon name="close" />
				<span className="u-vhide">{generalTranslations['btn_close']}</span>
			</button>
		</p>
	) : null;
};
