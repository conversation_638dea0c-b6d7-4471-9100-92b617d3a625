<?php

namespace App\Utils\HubspotFormService;

use App\Model\ConfigService;
use App\Model\Orm\State\State;
use App\Model\TranslatorDB;
use App\PostType\Contact\Model\Orm\Contact;
use App\PostType\Contact\Model\Orm\ContactLocalization;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Exception\GuzzleException;
use Nette\Utils\ArrayHash;
use Tracy\Debugger;

class HubspotFormService
{
	private Client $client;


	private array $config;

	public const FORM_TYPE_REGISTRATION = 'registration';
	public const FORM_TYPE_CONTACT = 'contact';
	public const FORM_TYPE_NEWSLETTER = 'newsletter';
	public const FORM_TYPE_REPAIR = 'repair';
	public const FORM_TYPE_CUSTOMIZED = 'customized';
	public const FORM_TYPE_INQUIRY = 'inquiry';


	public function __construct(
		private readonly ConfigService $configService,
		private readonly TranslatorDB $translator,
	)
	{
		$this->client = new Client();
		$this->config = $this->configService->getParam('hubspot');
	}

	/**
	 * Submits form data to HubSpot
	 *
	 * @param string $formType The type of form being submitted (use constants FORM_TYPE_*)
	 * @param ArrayHash $formData The form data to submit
	 * @param string $pageUrl The URL of the page where the form was submitted
	 * @param string $pageName The name of the page where the form was submitted
	 * @return array|null The response from HubSpot, or null if an error occurred
	 * @throws GuzzleException
	 */
	public function submitForm(string $formType, ArrayHash $formData, string $pageUrl, string $pageName, ?State $state = null, ?ContactLocalization $contactLocalization = null): ?array
	{
		// Construct the URL for HubSpot API
		$uid = $this->config[$formType]['uid'] ?? null;
		$url = sprintf(
			"%s%s/%s",
			$this->config['base_url'],
			$this->config['portal_id'],
			$uid
		);

		$fields = [];
		$index = 0;

		// Iterate through the form data to map local fields to HubSpot fields
		$fieldMappings = $this->config[$formType]['fields'] ?? [];
		foreach ($formData as $localKey => $value) {
			$mapping = $fieldMappings[$localKey] ?? null;

			// Skip unmapped fields
			if ($mapping === null) {
				continue;
			}

			// Get the HubSpot field name from the mapping
			$hubspotKey = $mapping['name'];

			// Populate field data including optional value mapping
			$fields[$index] = [
				'name' => $hubspotKey,
				'value' => $mapping['values'][$value] ?? $value, // Use mapped value if it exists
			];

			// Set `objectTypeId` if it's defined in the mapping
			if (!empty($mapping['objectTypeId'])) {
				$fields[$index]['objectTypeId'] = $mapping['objectTypeId'];
			} else {
				$fields[$index]['objectTypeId'] = '0-1';
			}

			//custom final data formating
			if ($hubspotKey === "firstname-lastname" && $localKey === "name") {
				//split name into firstname and lastname
				$nameParts = explode(' ', $value);
				$fields[$index]['name'] = "firstname";
				$fields[$index]['value'] = $nameParts[0] ?? '';

				//clone from firstname because of field objectTypeId
				$fields[$index + 1] = $fields[$index];
				$fields[$index + 1]['name'] = "lastname";
				$fields[$index + 1]['value'] = $nameParts[1] ?? '';
				//move index
				$index++;

			} else if ($hubspotKey === "region" && $localKey === "contact" && $state !== null) {
				//set regions names instead of ids
				if ($contactLocalization) {
					$regions = $this->translator->translate($contactLocalization->getParent()->getRegionsByState($state));
					$fields[$index]['value'] = $regions;
				}
			} else if ($hubspotKey === "state" && $localKey === "state") {
				//set state name instead of id
				if ($state !== null && !empty($state->name)) {
					$fields[$index]['value'] = $state->name;
				}
			}

			$index++;
		}

		// Build the request body in the required format
		$body = [
			'submittedAt' => (new \DateTime())->format('c'), // ISO 8601 timestamp
			'fields' => $fields,
			'context' => [
				'pageUri' => $pageUrl, // Page URL where the form is submitted
				'pageName' => $pageName, // Name of the page
				'hutk' => $_COOKIE['hubspotutk'] ?? null // Tracking cookie token from HubSpot tracking cookie
			],
			'legalConsentOptions' => [
				'consent' => [
					'consentToProcess' => true, // User consent to process data
					'text' => 'I agree to allow TON a.s. to store and process my personal data.',
					'communications' => [
						[
							'value' => false, // User consent for communications (newsletter, etc.)
							'subscriptionTypeId' => $this->config['subscription_type_id'] ?? 319280247,
							'text' => 'I agree to receive marketing communications from TON a.s.'
						]
					]
				]
			],
		];

		$communicationConsent = false;
		$removeCommunicationConsent = false;
		if (isset($formData->isNewsletter)) {
			$communicationConsent = (bool)$formData->isNewsletter;
		} elseif ($formType === self::FORM_TYPE_NEWSLETTER) {
			$communicationConsent = true;
		} else {
			$removeCommunicationConsent = true;
		}

		if ($removeCommunicationConsent) {
			unset($body['legalConsentOptions']['consent']['communications']);
		} else {
			$body['legalConsentOptions']['consent']['communications'][0]['value'] = $communicationConsent;
		}

		// Perform a POST request to the HubSpot API with the prepared body
		try {
			$response = $this->client->request('POST', $url, [
				'headers' => [
					'Content-Type' => 'application/json',
				],
				'json' => $body,
			]);

			// Decode the response
			// Return the JSON-decoded response from the API
			return json_decode($response->getBody()->getContents(), true);
		} catch (ClientException $e) {
			// Handle errors and extract error details from response
			if ($e->hasResponse()) {
				$response = $e->getResponse();
				$responseBody = json_decode($response->getBody()->getContents(), true);

				// Extract and dump errors from the response
				if (isset($responseBody['errors'])) {
					bdump($responseBody['errors']);
				} else {
					bdump($responseBody);
				}
			} else {
				bdump("Error without response: " . $e->getMessage());
			}
			return null;
		} catch (\Exception $e) {
			// Handle other exceptions
			bdump("Error: " . $e->getMessage());
			return null;
		}
	}
}
