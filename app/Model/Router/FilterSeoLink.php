<?php

declare(strict_types=1);

namespace App\Model\Router;

use App\Model\ConfigService;
use App\Model\Mutation\MutationHolder;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\PostType\Page\Model\Orm\CatalogTree;
use App\PostType\Page\Model\Orm\Tree;
use App\PostType\SeoLink\Model\Orm\SeoLinkLocalization;
use App\PostType\SeoLink\Model\SeoLinkModel;
use function array_key_exists;
use function count;
use function is_array;

final class FilterSeoLink
{

	public function __construct(
		private readonly SeoLinkModel $seoLinkModel,
		private readonly Orm $orm,
		private readonly MutationHolder $mutationHolder,
	)
	{
	}

	public function in(array $params): array
	{
		if ( ! isset($params['object'])) {
			return $params;
		}

		$seoLinkLocalization = null;
		if ($params['object'] instanceof SeoLinkLocalization) {
			$seoLinkLocalization = $params['object'];
		} else if ($params['object'] instanceof CatalogTree && isset($params['filter']))  {
			if (!is_array($params['filter'])) {
				$params['filter'] = [];
			}
			$seoLinkLocalization = $this->seoLinkModel->getByFilter($params['object']->mutation, $params['filter'], $params['object']);
		}

		if ($seoLinkLocalization === null) {
			return $params;
		}

		if ($seoLinkLocalization->category !== null) {
			$catalogPage = $seoLinkLocalization->category;
		} else {
			$catalogPage = $seoLinkLocalization->mutation->pages->eshop;
		}

		$params['object'] = $catalogPage;
		$params['idref'] = $catalogPage->id;

		$params['mutation'] = $seoLinkLocalization->mutation;
		$params['seoLink'] = $seoLinkLocalization;
		$params['presenter'] = 'Front:Catalog';
		$params['action'] = 'default';

		if (isset($params['filter']) && ! is_array($params['filter'])) {
			$params['filter'] = [];
		}

		$parameterValues = [];
		foreach ($seoLinkLocalization->seoLink->parameterValues as $parameterValue) {
			if ( ! array_key_exists($parameterValue->parameter->uid, $parameterValues)) {
				$parameterValues[$parameterValue->parameter->uid] = [];
			}

			$parameterValues[$parameterValue->parameter->uid][$parameterValue->id] = $parameterValue->id;
		}

		if ($parameterValues !== []) {
			$possibleParams = $this->orm->parameter->findBy(['uid' => array_keys($parameterValues)])->fetchPairs(null, 'uid');
			foreach ($possibleParams as $paramUid) {
				$params['filter']['dials'][$paramUid] = $parameterValues[$paramUid];
			}
		}

		return $params;
	}

	public function out(array $params): array
	{
        if (isset($params['seoLink'])) {
            unset($params['seoLink']);
        }
		if ( ! ($params['presenter'] === 'Front:Catalog' && $params['action'] === 'default')) {
			return $params;
		}

		if (isset($params['filter']) && is_array($params['filter']) && count($params['filter']) > 0) {


			if (isset($params['filter']['order'])) {
				$params['order'] = $params['filter']['order'];
			}

			if (isset($params['object']) && $params['object'] instanceof CatalogTree) {
				$mutation = $params['mutation'] ?? $this->mutationHolder->getMutation();
				$seoLink = $this->seoLinkModel->getByFilter($mutation, $params['filter'], $params['object']);

				if ($seoLink !== null && $seoLink->alias !== null) {
					$params['alias'] = $seoLink->alias->alias;
					unset($params['filter']);
				} else {
					$params['alias'] = $params['object']->alias->alias;
				}
			}
		}

		return $params;
	}


}
