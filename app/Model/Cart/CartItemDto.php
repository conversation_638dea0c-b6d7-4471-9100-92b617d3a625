<?php declare(strict_types=1);

namespace App\Model\Cart;

use App\Model\Orm\ProductVariant\ProductVariant;
use App\PostType\ProductInstance\Model\Orm\ProductInstanceLocalization;
use Nette\Utils\Random;

final readonly class CartItemDto
{
	public function __construct(
		public string $itemKey,
		public ProductVariant $variant,
		public string $selectedOptions,
		public ?int $count = null,
		public ?ProductInstanceLocalization $productInstanceLocalization = null,
	)
	{
	}
}
