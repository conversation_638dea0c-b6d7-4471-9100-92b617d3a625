{default $class = 'u-mb-xl'}
{default $button = false}
{default $crossroad = isset($object->crossroad) ? $object->crossroad : new ArrayIterator()}

<section class="c-authors {$class}">
	{if isset($customTitle) && $customTitle}
		<h2 class="c-articles__title">
			{translate}{$customTitle}{/translate}
		</h2>
	{/if}

	<div class="c-authors__list grid">
		{foreach $crossroad as $c}
			<div class="c-authors__item grid__cell size--6-12@sm size--4-12@md size--3-12@lg">
				{include '../box/author.latte', c=>$c}
			</div>
		{/foreach}
	</div>

	{if $button}
		<p class="u-ta-c u-mt-48">
			<a n:href="$pages->authors" class="btn btn--primary">
				<span class="btn__text">
					{_"authors_all"}
				</span>
			</a>
		</p>
	{/if}
</section>

