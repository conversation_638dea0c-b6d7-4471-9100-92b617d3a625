{default $class = false}
{default $groups = []}

<div n:if="count($groups)" n:ifcontent n:class="b-links, $class">
	<div n:ifcontent class="b-links__grid grid">
		{foreach $groups as $group}
			<div n:if="count($group->items ?? [])" class="b-links__cell grid__cell">
				<h3 n:if="$group->title ?? false" class="b-links__title decor">
					{$group->title}
				</h3>
				<ul class="b-links__list">
					<li n:foreach="$group->items as $item" n:ifcontent class="b-links__item">
						{define #btnContent}
							{default $icon = 'arrow-right'}
							<span n:if="isset($item->text)" class="btn__text">
								<span class="item-icon item-icon--after">
									{($icon)|icon, 'item-icon__icon'}
									<span class="item-icon__text">
										{$item->text}
									</span>
								</span>
							</span>
						{/define}

						{if $item->file ?? false}
							<a href="{$item->file->url|webalize}" n:ifcontent class="btn btn--secondary btn--transparent" download>
								{include #btnContent, icon=>'arrow-down'}
							</a>
						{elseif $item->page ?? false}
							<a href="{plink $item->page}" n:ifcontent class="btn btn--secondary btn--transparent">
								{include #btnContent, icon=>'arrow-right'}
							</a>
						{* {elseif $item->link ?? false}
							<a href="{$item->link}" n:ifcontent class="btn btn--secondary btn--transparent">
								{include #btnContent, icon=>'arrow-right'}
							</a> *}
						{/if}
					</li>
				</ul>
			</div>
		{/foreach}
	</div>
</div>