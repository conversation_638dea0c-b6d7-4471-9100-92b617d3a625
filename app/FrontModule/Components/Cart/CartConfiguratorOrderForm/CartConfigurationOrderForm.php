<?php declare(strict_types=1);

namespace App\FrontModule\Components\Cart\CartConfiguratorOrderForm;

use App\Components\MessageForForm\MessageForForm;
use App\Components\MessageForForm\MessageForFormFactory;
use App\FrontModule\Components\ConfigurationOrderForm\BaseConfigurationOrderForm;
use App\FrontModule\Components\ConfigurationOrderForm\StateContactModel;
use App\FrontModule\Components\FormHelpers\StateRegionHelper;
use App\FrontModule\Components\HasAntispamInput;

use App\FrontModule\Components\HasError500Catcher;
use App\Model\Cart\CartService;
use App\Model\Form\CommonFormFactory;
use App\Model\Gtm\Gtm;
use App\Model\Gtm\GtmProductViewEvent;
use App\Model\Link\LinkFactory;
use App\Model\Orm\ConfigurationOrder\ConfigurationOrderModel;
use App\Model\Orm\ConfigurationOrder\ConfigurationOrderRepository;
use App\Model\Orm\ConfigurationOrder\ConfigurationType;
use App\Model\Orm\ConfigurationOrder\CreateConfigurationOrderDto;
use App\Model\Orm\ConfigurationOrderItem\ConfigurationOrderItemModel;
use App\Model\Orm\ConfigurationOrderItem\ConfigurationOrderItemRepository;
use App\Model\Orm\ConfigurationOrderItem\CreateConfigurationOrderItemDto;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Orm\State\State;
use App\Model\Orm\State\StateRepository;
use App\Model\Orm\User\User;
use App\Model\TranslatorDB;
use App\PostType\Contact\Model\Orm\ContactLocalizationRepository;
use App\PostType\ProductInstance\Model\Orm\ProductInstanceLocalization;
use App\Utils\HubspotFormService\HubspotFormService;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nette\Utils\ArrayHash;
use Nextras\Orm\Collection\EmptyCollection;
use Nextras\Orm\Collection\ICollection;
use Tracy\Debugger;

/**
 * @property-read DefaultTemplate $template
 */
final class CartConfigurationOrderForm extends BaseConfigurationOrderForm
{

	use HasAntispamInput;
	use HasError500Catcher;

	private ?State $forcedState = null;
	private int $price = 0;
	private array $stateIdToContactIds;

	private ICollection $states;

	private ?ICollection $contactLocalizations = null;


	public function __construct(
		private readonly Mutation $mutation,
		private State $selectedState,
		private readonly StateRepository $stateRepository,
		private readonly ContactLocalizationRepository $contactLocalizationRepository,
		private readonly ConfigurationType $configurationType,
		private readonly MessageForFormFactory $messageForFormFactory,
		private readonly CommonFormFactory $commonFormFactory,
		private readonly TranslatorDB $translator,
		private readonly ConfigurationOrderModel $configurationOrderModel,
		private readonly ConfigurationOrderItemModel $configurationOrderItemModel,
		private readonly ConfigurationOrderRepository $configurationOrderRepository,
		private readonly ConfigurationOrderItemRepository $configurationOrderItemRepository,
		private readonly StateContactModel $stateContactModel,
		private readonly StateRegionHelper $stateRegionHelper,
		private readonly Gtm $gtm,
		private readonly LinkFactory $linkFactory,
		private readonly HubspotFormService $hubspotFormService,
		private readonly CartService $cartService,
		private readonly ?User $user = null,

	)
	{
		parent::__construct(
			$mutation,
			$selectedState,
			$stateRepository,
			$contactLocalizationRepository,
			$messageForFormFactory,
			$commonFormFactory,
			$translator,
			$stateContactModel,
			$stateRegionHelper,
		);
	}


	public function isCzechRepublic(): bool
	{
		if ($this->forcedState !== null) {
			if ($this->forcedState->code === State::DEFAULT_CODE) {
				return true;
			}
		} else if ($this->selectedState->code === State::DEFAULT_CODE) {
			return true;
		}
		return false;
	}

	protected function createComponentForm(): UI\Form
	{
		$form = $this->commonFormFactory->create();
		$form->setTranslator($this->translator);

		$this->stateRegionHelper->addState($form, $this->mutation, $this->selectedState, $this->getStates());
		$this->stateRegionHelper->addContact($form, $this->selectedState, $this->getContactLocalizations());

		$form->addText('name', 'form_label_name_and_surname')->setRequired()->setMaxLength(255);
		$form->addEmail('email', 'form_label_email')->setRequired()->setMaxLength(255);
		$form->addText('phone', 'form_label_telephone')->setRequired()->setMaxLength(255);
		$form->addTextArea('message', 'form_label_your_message')->setRequired();
		$form->addSubmit('send');

		if ($this->isCzechRepublic()) {
			$form->addText('zip', 'form_label_zip')->setRequired()->setMaxLength(50);
		} else {
			$form->addText('city', 'form_label_city')->setRequired()->setMaxLength(100);
		}

		$form->onSuccess[] = $this->formSucceeded(...);
		$form->onError[] = $this->formError(...);

		$this->attachAntispamTo($form);

		return $form;
	}

	public function formError(UI\Form $form): void
	{
		if (isset($form->getHttpData()['price'])) {
			$this->price = (int) $form->getHttpData()['price'];
		}
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}

	public function formSucceeded(UI\Form $form, ArrayHash $values): void
	{
		try {
			$allValues = $form->getHttpData();

			/** @var State $state */
			$state = $this->stateRepository->getByIdChecked($values->state);
			$contactLocalization = $this->contactLocalizationRepository->getByIdChecked($values->contact);

			$configurationOrder = $this->configurationOrderModel->createEntity(
				CreateConfigurationOrderDto::fromArray(array_merge($allValues, (array)$values,[
					'state' => $state,
					'mutation' => $this->mutation,
					'configurationType' => $this->configurationType,
					'contact' => $contactLocalization->contact,
					'user' => $this->user,
				])),
			);

			foreach ($this->cartService->getAllItems() as $cartItem) {
				$this->configurationOrderItemModel->createEntity(
					CreateConfigurationOrderItemDto::from($configurationOrder, $cartItem)
				);
			}


			$this->configurationOrderRepository->flush();
			$this->configurationOrderItemRepository->flush();

			$this->flashMessage('form_configurator_order_ok', 'ok');

			$this->clearCommonFormFields($form);

			$this->price = $this->cartService->getTotalPrice($this->mutation)->getMinorAmount()->toInt();

			//TODO adjust gtm for multiple items
//			$this->gtm->pushEvent((new GtmProductViewEvent($this->gtm))
//				->setup(
//					mutation: $this->mutation,
//					variant: $configurationOrderItem->variant,
//					quantity: $configurationOrderItem->count,
//					price: $this->price,
//					state: $configurationOrder->state,
//					forcedName: 'item_requested',
//				)
//			);

			//hubspot
			//TODO what to do with this
//			$productLocalization = $this->variant->product->getLocalization($this->mutation);
//			if($productLocalization === null) {
//				Debugger::log('Missing product localization for mutation ' . $this->mutation->langCode, 'error');
//				$this->flashMessage('form_configurator_order_fail', 'error');
//			}else{
//				$values->pageLink = $this->linkFactory->linkTranslateToNette($productLocalization, ['v' => $this->variant->id]);
//				$values->page = $productLocalization->getNameTitle();
//				$this->hubspotFormService->submitForm(HubspotFormService::FORM_TYPE_INQUIRY, $values, $values->pageLink, $values->page, $state, $contactLocalization);
//			}

		} catch (\Throwable $e) {
			$this->flashMessage('form_configurator_order_fail', 'error');
		}


		if ($this->presenter->isAjax()) {
			$this->presenter->redrawControl('gtmEvents');
			$this->redrawControl();
		} else {
			$this->presenter->redirect('this');
		}
	}

	public function render(): void
	{
		try {
			$this->template->setTranslator($this->translator);
			$this->template->price = $this->price;
			$this->template->render(__DIR__ . '/configurationOrderForm.latte');
		} catch (\Throwable $e) {
			/** @noinspection PhpUnhandledExceptionInspection */
			$this->handleRenderError500($e);
		}

	}

	protected function createComponentMessageForForm(): MessageForForm
	{
		return $this->messageForFormFactory->create($this->translator);
	}

	public function handleLoadRegions(int $stateId): void
	{
		$this->setState($this->stateRepository->getByIdChecked($stateId));
		if ($this->presenter->isAjax()) {
			$this->redrawControl('regions');
			$this->redrawControl('zip');
		}
	}

}
