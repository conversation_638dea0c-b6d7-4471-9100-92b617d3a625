{snippet form}
	{form form class: 'f-enquiry block-loader',
		novalidate: "novalidate",
		data-naja: "",
		data-naja-history: "off"
	}
		<h2 class="u-mb-md h1">
			{_"title_form_enquiry"}
		</h2>

		{control messageForForm, $flashes, $form}

		<div class="u-mb-last-0 u-mb-sm" data-controller="regions" data-regions-parameter-value="configurationOrderForm-stateId">

			{*ANTISPAM*}
			{if isset($form['antispamNoJs'])}
				<p n:class="$form['antispamNoJs']->hasErrors() ? 'has-error' : 'u-js-hide'" data-controller="antispam">
					<label n:name="antispamNoJs">
						{_$form['antispamNoJs']->caption|noescape} {if $form['antispamNoJs']->isRequired()}*{/if}
					</label>
					<span class="inp-fix">
					<input n:name="antispamNoJs" class="inp-text" data-antispam-target="input">
				</span>
				</p>
			{/if}
			{*/ANTISPAM*}
			<input n:name="_do">

			<div class="grid grid--y-0">
				<div class="grid__cell size--0-12@sm">
					{include '../inp.latte', form: $form, name: state, pClass: 'f-std__inp', labelClass: 'inp-label', labelLang: 'form_label_state', dataAction=>'change->regions#load'}
					<a class="u-d-n" n:href="loadRegions!, stateId=>61" data-naja="" data-naja-loader=".f-enquiry" data-naja-history="off" data-regions-target="link" >Load regions</a>
					{* <a class="u-d-n" n:href="loadRegions!, stateId=>11" data-naja="" data-naja-history="off" >loadRegion bad</a> *}
				</div>
				<div class="grid__cell size--6-12@sm">
					{snippet regions}
						<form n:name=form n:tag-if=false>
							{include '../inp.latte', form: $form, name: contact, pClass: 'f-std__inp', labelClass: 'inp-label', labelLang: 'form_label_region'}
							{if isset($form['city'])}
								{include '../inp.latte', form: $form, name: city, pClass: 'f-std__inp', labelClass: 'inp-label', labelLang: 'form_label_city'}
							{/if}
						</form>
					{/snippet}
				</div>
				<div class="grid__cell size--6-12@sm">
					{snippet zip}
						<form n:name=form n:tag-if=false>
							{if isset($form['zip'])}
								{include '../inp.latte', form: $form, name: zip, pClass: 'f-std__inp', labelClass: 'inp-label', labelLang: 'form_label_zip'}
							{/if}
						</form>
					{/snippet}
				</div>
				<div class="grid__cell size--6-12@sm">
					{include '../inp.latte', form: $form, name: name, pClass: 'f-std__inp', labelClass: 'inp-label', labelLang: 'form_label_name_and_surname'}
				</div>
				<div class="grid__cell size--6-12@sm">
					{include '../inp.latte', form: $form, name: phone, pClass: 'f-std__inp', labelClass: 'inp-label', labelLang: 'form_label_telephone', type: 'tel'}
				</div>
				<div class="grid__cell size--6-12@sm">
					{include '../inp.latte', form: $form, name: email, pClass: 'f-std__inp', labelClass: 'inp-label', labelLang: 'form_label_email'}
				</div>

				<div n:if="isset($form['count'])" class="grid__cell size--6-12@sm">
					{include '../inp.latte', form: $form, name: count, pClass: 'f-std__inp', labelClass: 'inp-label', labelLang: 'form_label_pieces'}
				</div>
				{* <div class="grid__cell size--6-12@sm"> *}
					{* Počet kusů *}
				{* </div> *}
			</div>

			{include '../inp.latte', rows: 6, form: $form, name: message, pClass: 'f-std__inp', labelClass: 'inp-label', labelLang: 'form_label_your_message'}

			{* {if isset($pages->conditions)}
				{capture $termsLink}{plink $pages->conditions}{/capture}
				{include '../inp.latte', form: $form, name: agree, pClass: 'f-std__inp', labelClass: 'f-std__label inp-label', labelLang: 'form_label_terms', labelReplace=>array('%link' => $termsLink->__toString())}
			{/if} *}
			<input id="configurator-selected-options" type="hidden" name="selectedOptions" value="{$selectedOptions}">
			<input id="configurator-price" type="hidden" name="price" value="{$price}">
		</div>

		<p class="decor u-mb-md">
			{_"fields_required"}
		</p>

		<p class="u-mb-0">
			<button type="submit" class="btn btn--transparent">
				<span class="btn__text">
					{_'btn_send_enquiry'}
				</span>
			</button>
		</p>


		<div class="block-loader__loader"></div>
	{/form}
{/snippet}

