<?php declare(strict_types=1);

namespace App\FrontModule\Components\ConfigurationOrderForm;

use App\Model\Orm\ConfigurationOrder\ConfigurationType;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Orm\State\State;
use App\Model\Orm\User\User;
use App\PostType\ProductInstance\Model\Orm\ProductInstanceLocalization;

interface ConfigurationOrderFormFactory
{

	public function create(
		ProductVariant $variant,
		Mutation $mutation,
		State $selectedState,
		ConfigurationType $configurationType,
		string $selectedOptions = '',
		?User $user = null,
		?ProductInstanceLocalization $productInstanceLocalization = null,
	): ConfigurationOrderForm;

}
