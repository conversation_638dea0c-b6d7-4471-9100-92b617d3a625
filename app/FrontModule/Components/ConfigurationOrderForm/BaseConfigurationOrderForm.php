<?php declare(strict_types=1);

namespace App\FrontModule\Components\ConfigurationOrderForm;

use App\Components\MessageForForm\MessageForForm;
use App\Components\MessageForForm\MessageForFormFactory;
use App\FrontModule\Components\FormHelpers\StateRegionHelper;
use App\FrontModule\Components\HasAntispamInput;
use App\FrontModule\Components\HasError500Catcher;
use App\Model\Form\CommonFormFactory;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\State\State;
use App\Model\Orm\State\StateRepository;
use App\Model\TranslatorDB;
use App\PostType\Contact\Model\Orm\ContactLocalizationRepository;
use Nette\Application\UI;
use Nextras\Orm\Collection\EmptyCollection;
use Nextras\Orm\Collection\ICollection;

/**
 * Base class for configuration order forms containing common functionality
 * that was duplicated across multiple form implementations.
 */
abstract class BaseConfigurationOrderForm extends UI\Control
{
    use HasAntispamInput;
    use Has<PERSON>rror500Catcher;

    // Common properties extracted from duplicated code
    protected ?State $forcedState = null;
    protected int $price = 0;
    protected array $stateIdToContactIds;
    protected ICollection $states;
    protected ?ICollection $contactLocalizations = null;

    public function __construct(
        protected readonly Mutation $mutation,
        protected State $selectedState,
        protected readonly StateRepository $stateRepository,
        protected readonly ContactLocalizationRepository $contactLocalizationRepository,
        protected readonly MessageForFormFactory $messageForFormFactory,
        protected readonly CommonFormFactory $commonFormFactory,
        protected readonly TranslatorDB $translator,
        protected readonly StateContactModel $stateContactModel,
        protected readonly StateRegionHelper $stateRegionHelper,
    ) {
        $this->onAnchor[] = $this->init(...);
    }

    /**
     * Initialize form state from POST data - duplicated across forms
     */
    protected function init(): void
    {
        if ($this->presenter->getRequest()!== null && $this->presenter->getRequest()->getPost('state') !== null && $this->presenter->getRequest()->getPost('state') !== "") {
            $stateId = (int) $this->presenter->getRequest()->getPost('state');
            $this->setState($this->stateRepository->getByIdChecked($stateId));
        }
    }

    /**
     * Get state to contact IDs mapping - duplicated across forms
     */
    protected function getStateIdToContactIds(): array
    {
        if (!isset($this->stateIdToContactIds)) {
            $this->stateIdToContactIds = $this->stateContactModel->getCountryMapForMutation($this->mutation);
        }

        return $this->stateIdToContactIds;
    }

    /**
     * Check if Czech Republic is selected - duplicated across forms
     */
    public function isCzechRepublic(): bool
    {
        if ($this->forcedState !== null) {
            if ($this->forcedState->code === State::DEFAULT_CODE) {
                return true;
            }
        } else if ($this->selectedState->code === State::DEFAULT_CODE) {
            return true;
        }
        return false;
    }

    /**
     * Get available states - duplicated across forms
     */
    protected function getStates(): ICollection
    {
        if (!isset($this->states)) {
            $this->states = $this->stateRepository->findByIds(array_keys($this->getStateIdToContactIds()));
        }
        return $this->states;
    }

    /**
     * Get contact localizations for selected state - duplicated across forms
     */
    protected function getContactLocalizations(): ICollection
    {
        if ($this->contactLocalizations === null) {
            if (isset($this->getStateIdToContactIds()[$this->selectedState->id])) {
                $this->contactLocalizations = $this->contactLocalizationRepository->findByIds($this->getStateIdToContactIds()[$this->selectedState->id]);
            } else {
                $this->contactLocalizations = new EmptyCollection();
            }
        }
        return $this->contactLocalizations;
    }

    /**
     * Set new state and clear contact localizations - duplicated across forms
     */
    protected function setState(State $newState): void
    {
        $this->contactLocalizations = null;
        $this->selectedState = $newState;
    }

    /**
     * AJAX handler for loading regions - duplicated across forms
     */
    public function handleLoadRegions(int $stateId): void
    {
        $this->setState($this->stateRepository->getByIdChecked($stateId));
        if ($this->presenter->isAjax()) {
            $this->redrawControl('regions');
            $this->redrawControl('zip');
        }
    }

    /**
     * Create message component - duplicated across forms
     */
    protected function createComponentMessageForForm(): MessageForForm
    {
        return $this->messageForFormFactory->create($this->translator);
    }

    /**
     * Common form error handling - duplicated across forms
     */
    public function formError(UI\Form $form): void
    {
        if (isset($form->getHttpData()['price'])) {
            $this->price = (int) $form->getHttpData()['price'];
        }
        if ($this->presenter->isAjax()) {
            $this->redrawControl();
        }
    }

    /**
     * Add common form fields that are duplicated across forms
     */
    protected function addCommonFormFields(UI\Form $form): void
    {
        // State and contact fields
        $this->stateRegionHelper->addState($form, $this->mutation, $this->selectedState, $this->getStates());
        $this->stateRegionHelper->addContact($form, $this->selectedState, $this->getContactLocalizations());

        // Common contact fields
        $form->addText('name', 'form_label_name_and_surname')->setRequired()->setMaxLength(255);
        $form->addEmail('email', 'form_label_email')->setRequired()->setMaxLength(255);
        $form->addText('phone', 'form_label_telephone')->setRequired()->setMaxLength(255);
        $form->addTextArea('message', 'form_label_your_message')->setRequired();

        // Location-specific fields
        if ($this->isCzechRepublic()) {
            $form->addText('zip', 'form_label_zip')->setRequired()->setMaxLength(50);
        } else {
            $form->addText('city', 'form_label_city')->setRequired()->setMaxLength(100);
        }
    }

    /**
     * Clear common form fields after successful submission - duplicated across forms
     */
    protected function clearCommonFormFields(UI\Form $form): void
    {
        $form['name']->setValue('');
        $form['email']->setValue('');
        $form['phone']->setValue('');
        $form['message']->setValue('');

        if (isset($form['zip'])) {
            $form['zip']->setValue('');
        }
        if (isset($form['city'])) {
            $form['city']->setValue('');
        }
    }

    /**
     * Common render setup - duplicated across forms
     */
    protected function setupRender(): void
    {
        $this->template->setTranslator($this->translator);
        $this->template->price = $this->price;
    }

    /**
     * Handle common AJAX redirect logic - duplicated across forms
     */
    protected function handleAjaxRedirect(): void
    {
        if ($this->presenter->isAjax()) {
            $this->redrawControl();
        }
    }

    /**
     * Extract state and contact localization from form values - duplicated across forms
     * This fragment was duplicated in all configuration order forms
     */
    protected function extractStateAndContact(\Nette\Utils\ArrayHash $values): array
    {
        /** @var State $state */
        $state = $this->stateRepository->getByIdChecked($values->state);
        $contactLocalization = $this->contactLocalizationRepository->getByIdChecked($values->contact);

        return [$state, $contactLocalization];
    }

    // Abstract methods that child classes must implement
    abstract protected function createComponentForm(): UI\Form;
    abstract public function render(): void;
}
